<?php

namespace App\Http\Controllers\Auth;

use App\Traits\Passport\PassportRedirectTrait;
use App\Traits\Passport\PassportSessionTrait;
use App\Traits\UserLogoutTrait;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Laravel\Passport\Bridge\User;
use Laravel\Passport\ClientRepository;
use Laravel\Passport\Contracts\AuthorizationViewResponse;
use Laravel\Passport\Http\Controllers\AuthorizationController as PassportAuthorizationController;
use <PERSON><PERSON>\Passport\TokenRepository;
use League\OAuth2\Server\AuthorizationServer;
use Nyholm\Psr7\Response as Psr7Response;
use Psr\Http\Message\ServerRequestInterface;

class AuthorizationController extends PassportAuthorizationController
{
    use PassportSessionTrait;
    use PassportRedirectTrait;
    use UserLogoutTrait;

    /**
     * Create a new controller instance.
     *
     * @param  \League\OAuth2\Server\AuthorizationServer  $server
     * @param  \Illuminate\Contracts\Auth\StatefulGuard  $guard
     * @param  \Laravel\Passport\Contracts\AuthorizationViewResponse  $response
     * @return void
     */
    public function __construct(
        AuthorizationServer $server,
        StatefulGuard $guard,
        AuthorizationViewResponse $response,
    ) {
        $this->server = $server;
        $this->guard = $guard;
        $this->response = $response;
    }


    /**
     * Authorize a client to access the user's account.
     *
     * @param  \Psr\Http\Message\ServerRequestInterface  $psrRequest
     * @param  \Illuminate\Http\Request  $request
     * @param  \Laravel\Passport\ClientRepository  $clients
     * @param  \Laravel\Passport\TokenRepository  $tokens
     * @return \Illuminate\Http\Response|\Laravel\Passport\Contracts\AuthorizationViewResponse
     */
    #[\Override]
    public function authorize(
        ServerRequestInterface $psrRequest,
        Request $request,
        ClientRepository $clients,
        TokenRepository $tokens,
    ) {
        // Handle registration flow logic before authorization
        $this->handleRegistrationFlowLogic($request);

        // Clear any conflicting session data from previous OAuth flows
        $this->clearConflictingSessionData();

        // Regenerate session to ensure clean state for this OAuth request
        session()->regenerate(true); // true means delete the old session

        // Set up session for this specific OAuth request
        $this->initializeOAuthSession($request);

        return parent::authorize($psrRequest, $request, $clients, $tokens);
    }

    /**
     * Handle registration flow logic before OAuth authorization.
     *
     * If user is logged in AND is_register=1 parameter is present, logout the user.
     * This ensures the registration flow starts with a clean slate.
     *
     * @param \Illuminate\Http\Request $request
     * @return void
     */
    private function handleRegistrationFlowLogic(Request $request): void
    {
        // Check if this is a registration flow request and user is logged in
        if ($request->has('is_register') && $request->get('is_register') == '1' && Auth::check()) {
            // Log out the user to ensure clean registration flow
            Auth::logout();

            // Clear any existing session data that might interfere
            session()->forget(['redirect_uri', 'authRequest']);

            // Regenerate session to ensure clean state
            session()->regenerate(true);
        }
    }

    /**
     * Clear conflicting session data from previous OAuth flows.
     *
     * This ensures that session data from previous OAuth requests doesn't
     * interfere with the current request, especially when switching between
     * login and registration flows.
     *
     * @return void
     */
    private function clearConflictingSessionData(): void
    {
        // Always clear these session keys to prevent cross-request interference
        $keysToForget = [
            'redirect_uri',
            'authRequest',
            'is_first_time',
            'oauth_request_id', // Custom key to track specific OAuth requests
            'oauth_flow_type'   // Track whether this is login or registration flow
        ];

        session()->forget($keysToForget);
    }

    /**
     * Initialize session for this specific OAuth request.
     *
     * Sets up session variables that are specific to this OAuth authorization
     * request to prevent interference from other requests.
     *
     * @param \Illuminate\Http\Request $request
     * @return void
     */
    private function initializeOAuthSession(Request $request): void
    {
        // Generate a unique identifier for this OAuth request
        $oauthRequestId = uniqid('oauth_', true);
        session()->put('oauth_request_id', $oauthRequestId);

        // Determine and store the flow type
        $flowType = $request->has('is_register') && $request->get('is_register') == '1' ? 'registration' : 'login';
        session()->put('oauth_flow_type', $flowType);

        // Set is_first_time flag for this specific request
        session()->put('is_first_time', true);

        // Store the current request URL for this OAuth flow
        $currentUrl = $request->fullUrl();
        session()->put('redirect_uri', $currentUrl);
    }

    /**
     * Approve the authorization request.
     *
     * @param \League\OAuth2\Server\RequestTypes\AuthorizationRequest $authRequest
     * @param \Illuminate\Contracts\Auth\Authenticatable              $user
     *
     * @return \Illuminate\Http\Response
     */
    #[\Override]
    protected function approveRequest($authRequest, $user)
    {
        $authRequest->setUser(new User($user->getAuthIdentifier()));

        $authRequest->setAuthorizationApproved(true);

        return $this->withErrorHandling(function () use ($authRequest) {
            $response = $this->convertResponse(
                $this->server->completeAuthorizationRequest($authRequest, new Psr7Response()),
            );

            $this->storeAgent($response);

            return $response;
        });
    }
}
