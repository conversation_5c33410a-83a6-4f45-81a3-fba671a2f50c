<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;

abstract class TestCase extends BaseTestCase
{
    /**
     * Setup the test environment.
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Ensure we're using the testing environment
        $this->app['env'] = 'testing';
    }

    /**
     * Assert that we're using the SQLite testing database.
     */
    protected function assertUsingTestDatabase(): void
    {
        $this->assertEquals('testing', app()->environment());
        $this->assertEquals('sqlite', config('database.default'));
        $this->assertEquals(':memory:', config('database.connections.sqlite.database'));
    }

    /**
     * Assert that development database configuration is preserved.
     */
    protected function assertDevelopmentDatabasePreserved(): void
    {
        // Verify that the original .env file still has MySQL configuration
        $envContent = file_get_contents(base_path('.env'));
        $this->assertStringContainsString('DB_CONNECTION=mysql', $envContent);
        $this->assertStringContainsString('DB_DATABASE=gravitywrite_db', $envContent);
    }

    /**
     * Get the current database connection info for debugging.
     */
    protected function getDatabaseInfo(): array
    {
        return [
            'environment' => app()->environment(),
            'default_connection' => config('database.default'),
            'sqlite_database' => config('database.connections.sqlite.database'),
            'mysql_database' => config('database.connections.mysql.database'),
        ];
    }
}
