<?php

namespace Tests\Feature\Auth;

use App\Models\Passport\Client;
use App\Models\Passport\OAuthSession;
use App\Models\Passport\RefreshToken;
use App\Models\Passport\Token;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class TokenManagementTest extends TestCase
{
    use RefreshDatabase;

    protected Client $oauthClient;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Verify database isolation
        $this->assertUsingTestDatabase();
        
        // Create test OAuth client
        $this->oauthClient = Client::factory()->create([
            'id' => 'token-test-client',
            'secret' => 'token-test-secret',
        ]);
        
        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'verified' => 1,
        ]);
    }

    public function test_database_isolation_for_token_tests()
    {
        $this->assertUsingTestDatabase();
        $this->assertDevelopmentDatabasePreserved();
        
        // Verify clean state
        $this->assertEquals(1, User::count());
        $this->assertEquals(1, Client::count());
        $this->assertEquals(0, Token::count());
    }

    public function test_token_creation_and_validation()
    {
        $this->assertUsingTestDatabase();
        
        // Create a token
        $token = Token::factory()->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Verify token was created
        $this->assertDatabaseHas('oauth_access_tokens', [
            'id' => $token->id,
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
        ]);
        
        // Test token usage
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->id,
        ])->get('/api/user');
        
        $response->assertStatus(200);
        $response->assertJson([
            'id' => $this->user->id,
            'email' => $this->user->email,
        ]);
    }

    public function test_token_expiration_handling()
    {
        $this->assertUsingTestDatabase();
        
        // Create an expired token
        $expiredToken = Token::factory()->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->subHour(),
            'revoked' => false,
        ]);
        
        // Try to use expired token
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $expiredToken->id,
        ])->get('/api/user');
        
        $response->assertStatus(401);
    }

    public function test_token_revocation()
    {
        $this->assertUsingTestDatabase();
        
        // Create a valid token
        $token = Token::factory()->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Verify token works
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->id,
        ])->get('/api/user');
        
        $response->assertStatus(200);
        
        // Revoke the token
        $token->revoke();
        
        // Verify token is revoked in database
        $this->assertDatabaseHas('oauth_access_tokens', [
            'id' => $token->id,
            'revoked' => true,
        ]);
        
        // Try to use revoked token
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->id,
        ])->get('/api/user');
        
        $response->assertStatus(401);
    }

    public function test_refresh_token_functionality()
    {
        $this->assertUsingTestDatabase();
        
        // Create an access token
        $accessToken = Token::factory()->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Create associated refresh token
        $refreshToken = RefreshToken::factory()->create([
            'access_token_id' => $accessToken->id,
            'expires_at' => now()->addDays(30),
            'revoked' => false,
        ]);
        
        // Verify refresh token was created
        $this->assertDatabaseHas('oauth_refresh_tokens', [
            'id' => $refreshToken->id,
            'access_token_id' => $accessToken->id,
            'revoked' => false,
        ]);
        
        // Test refresh token usage
        $response = $this->post('/oauth/token', [
            'grant_type' => 'refresh_token',
            'refresh_token' => $refreshToken->id,
            'client_id' => $this->oauthClient->id,
            'client_secret' => $this->oauthClient->secret,
        ]);
        
        // Should attempt to refresh (may fail due to missing implementation details)
        $this->assertNotEquals(404, $response->status());
    }

    public function test_multiple_tokens_per_user()
    {
        $this->assertUsingTestDatabase();
        
        // Create multiple tokens for the same user
        $tokens = Token::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Verify all tokens work
        foreach ($tokens as $token) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token->id,
            ])->get('/api/user');
            
            $response->assertStatus(200);
        }
        
        // Verify user has multiple tokens
        $userTokenCount = Token::where('user_id', $this->user->id)->count();
        $this->assertEquals(3, $userTokenCount);
    }

    public function test_token_scopes_handling()
    {
        $this->assertUsingTestDatabase();
        
        // Create token with specific scopes
        $scopedToken = Token::factory()->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'scopes' => json_encode(['read', 'write']),
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Verify scopes are stored
        $this->assertDatabaseHas('oauth_access_tokens', [
            'id' => $scopedToken->id,
            'scopes' => json_encode(['read', 'write']),
        ]);
        
        // Test token usage (scope validation depends on implementation)
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $scopedToken->id,
        ])->get('/api/user');
        
        $response->assertStatus(200);
    }

    public function test_user_token_cleanup_on_deletion()
    {
        $this->assertUsingTestDatabase();
        
        // Create tokens for the user
        $tokens = Token::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Create OAuth sessions
        $sessions = OAuthSession::factory()->count(2)->forUser($this->user)->create();
        
        // Verify tokens and sessions exist
        $this->assertEquals(2, Token::where('user_id', $this->user->id)->count());
        $this->assertEquals(2, OAuthSession::where('user_id', $this->user->id)->count());
        
        // Delete the user
        $this->user->delete();
        
        // Verify OAuth sessions are cleaned up (due to foreign key constraint)
        $this->assertEquals(0, OAuthSession::where('user_id', $this->user->id)->count());
        
        // Tokens might still exist (depending on cascade settings)
        // This is acceptable as they become invalid without a user
    }

    public function test_client_token_cleanup_on_revocation()
    {
        $this->assertUsingTestDatabase();
        
        // Create tokens for the client
        $tokens = Token::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Verify tokens exist
        $this->assertEquals(3, Token::where('client_id', $this->oauthClient->id)->count());
        
        // Revoke the client
        $this->oauthClient->update(['revoked' => true]);
        
        // Tokens should still exist but client is revoked
        $this->assertEquals(3, Token::where('client_id', $this->oauthClient->id)->count());
        
        // Try to use tokens with revoked client
        foreach ($tokens as $token) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token->id,
            ])->get('/api/user');
            
            // Should fail because client is revoked
            $this->assertEquals(401, $response->status());
        }
    }

    public function test_token_introspection()
    {
        $this->assertUsingTestDatabase();
        
        // Create a token
        $token = Token::factory()->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Test token introspection via API call
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->id,
        ])->get('/api/user');
        
        $response->assertStatus(200);
        
        // Verify user data is returned correctly
        $userData = $response->json();
        $this->assertEquals($this->user->id, $userData['id']);
        $this->assertEquals($this->user->email, $userData['email']);
    }

    public function test_concurrent_token_usage()
    {
        $this->assertUsingTestDatabase();
        
        // Create a token
        $token = Token::factory()->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Make multiple concurrent requests with the same token
        $responses = [];
        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token->id,
            ])->get('/api/user');
        }
        
        // All requests should succeed
        foreach ($responses as $response) {
            $response->assertStatus(200);
        }
    }

    public function test_token_with_different_clients()
    {
        $this->assertUsingTestDatabase();
        
        // Create another client
        $anotherClient = Client::factory()->create([
            'id' => 'another-client',
            'secret' => 'another-secret',
        ]);
        
        // Create tokens for different clients
        $token1 = Token::factory()->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        $token2 = Token::factory()->create([
            'user_id' => $this->user->id,
            'client_id' => $anotherClient->id,
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Both tokens should work
        $response1 = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token1->id,
        ])->get('/api/user');
        
        $response2 = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token2->id,
        ])->get('/api/user');
        
        $response1->assertStatus(200);
        $response2->assertStatus(200);
    }

    protected function tearDown(): void
    {
        // Verify database isolation is maintained
        $this->assertUsingTestDatabase();
        
        parent::tearDown();
    }
}
