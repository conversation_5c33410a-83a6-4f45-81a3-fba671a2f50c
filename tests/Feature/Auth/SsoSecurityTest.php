<?php

namespace Tests\Feature\Auth;

use App\Models\Passport\Client;
use App\Models\Passport\Token;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class SsoSecurityTest extends TestCase
{
    use RefreshDatabase;

    protected Client $oauthClient;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Verify database isolation
        $this->assertUsingTestDatabase();
        
        // Create test OAuth client
        $this->oauthClient = Client::factory()->create([
            'id' => 'security-test-client',
            'secret' => Hash::make('secure-secret'),
        ]);
        
        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'verified' => 1,
        ]);
    }

    public function test_database_isolation_for_security_tests()
    {
        $this->assertUsingTestDatabase();
        $this->assertDevelopmentDatabasePreserved();
        
        // Verify clean state
        $this->assertEquals(1, User::count());
        $this->assertEquals(1, Client::count());
    }

    public function test_client_secret_verification_with_hashed_secrets()
    {
        $this->assertUsingTestDatabase();
        
        // Test with correct secret
        $response = $this->post('/oauth/token', [
            'grant_type' => 'client_credentials',
            'client_id' => $this->oauthClient->id,
            'client_secret' => 'secure-secret',
            'scope' => '',
        ]);
        
        // Should not fail due to client authentication
        $this->assertNotEquals(401, $response->status());
        
        // Test with incorrect secret
        $response = $this->post('/oauth/token', [
            'grant_type' => 'client_credentials',
            'client_id' => $this->oauthClient->id,
            'client_secret' => 'wrong-secret',
            'scope' => '',
        ]);
        
        // Should fail with client authentication error
        $this->assertEquals(401, $response->status());
    }

    public function test_authorization_code_grant_client_validation()
    {
        $this->assertUsingTestDatabase();
        
        // Test authorization code grant with correct client credentials
        $response = $this->post('/oauth/token', [
            'grant_type' => 'authorization_code',
            'client_id' => $this->oauthClient->id,
            'client_secret' => 'secure-secret',
            'redirect_uri' => $this->oauthClient->redirect,
            'code' => 'test-authorization-code',
        ]);
        
        // Should not fail due to client authentication (may fail for invalid code)
        $this->assertNotEquals(401, $response->status());
        
        // Test with non-existent client
        $response = $this->post('/oauth/token', [
            'grant_type' => 'authorization_code',
            'client_id' => 'non-existent-client',
            'client_secret' => 'any-secret',
            'redirect_uri' => 'http://example.com/callback',
            'code' => 'test-authorization-code',
        ]);
        
        // Should fail with client not found
        $this->assertEquals(401, $response->status());
    }

    public function test_revoked_client_access_denied()
    {
        $this->assertUsingTestDatabase();
        
        // Revoke the client
        $this->oauthClient->update(['revoked' => true]);
        
        // Try to use revoked client
        $response = $this->post('/oauth/token', [
            'grant_type' => 'client_credentials',
            'client_id' => $this->oauthClient->id,
            'client_secret' => 'secure-secret',
            'scope' => '',
        ]);
        
        // Should fail with client authentication error
        $this->assertEquals(401, $response->status());
    }

    public function test_token_expiration_functionality()
    {
        $this->assertUsingTestDatabase();
        
        // Create an expired token
        $expiredToken = Token::factory()->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->subDay(),
            'revoked' => false,
        ]);
        
        // Try to use expired token
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $expiredToken->id,
        ])->get('/api/user');
        
        // Should fail with unauthorized
        $this->assertEquals(401, $response->status());
    }

    public function test_token_revocation_functionality()
    {
        $this->assertUsingTestDatabase();
        
        // Create a valid token
        $token = Token::factory()->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Verify token works
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->id,
        ])->get('/api/user');
        
        $this->assertEquals(200, $response->status());
        
        // Revoke the token
        $token->update(['revoked' => true]);
        
        // Try to use revoked token
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->id,
        ])->get('/api/user');
        
        // Should fail with unauthorized
        $this->assertEquals(401, $response->status());
    }

    public function test_multiple_token_revocation_for_user()
    {
        $this->assertUsingTestDatabase();
        
        // Create multiple tokens for the user
        $tokens = Token::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Verify all tokens work
        foreach ($tokens as $token) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token->id,
            ])->get('/api/user');
            
            $this->assertEquals(200, $response->status());
        }
        
        // Revoke all tokens for the user
        Token::where('user_id', $this->user->id)->update(['revoked' => true]);
        
        // Verify all tokens are now invalid
        foreach ($tokens as $token) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token->id,
            ])->get('/api/user');
            
            $this->assertEquals(401, $response->status());
        }
    }

    public function test_oauth_scope_validation()
    {
        $this->assertUsingTestDatabase();
        
        // Create token with limited scope
        $limitedToken = Token::factory()->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'scopes' => json_encode(['read-only']),
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Test access with limited scope token
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $limitedToken->id,
        ])->get('/api/user');
        
        // Should work for read operations
        $this->assertEquals(200, $response->status());
    }

    public function test_client_redirect_uri_validation()
    {
        $this->assertUsingTestDatabase();
        
        // Try authorization with invalid redirect URI
        $response = $this->actingAs($this->user)->get('/oauth/authorize?' . http_build_query([
            'client_id' => $this->oauthClient->id,
            'redirect_uri' => 'http://malicious-site.com/callback',
            'response_type' => 'code',
            'scope' => '',
        ]));
        
        // Should fail or redirect to error
        $this->assertTrue($response->status() >= 400 || $response->isRedirection());
    }

    public function test_csrf_protection_on_oauth_endpoints()
    {
        $this->assertUsingTestDatabase();
        
        // Try to approve authorization without CSRF token
        $response = $this->actingAs($this->user)->post('/oauth/authorize', [
            'client_id' => $this->oauthClient->id,
            'redirect_uri' => $this->oauthClient->redirect,
            'response_type' => 'code',
            'scope' => '',
        ]);
        
        // Should fail with CSRF error
        $this->assertEquals(419, $response->status());
    }

    public function test_rate_limiting_on_token_endpoint()
    {
        $this->assertUsingTestDatabase();
        
        // Make multiple rapid requests to token endpoint
        $responses = [];
        for ($i = 0; $i < 10; $i++) {
            $responses[] = $this->post('/oauth/token', [
                'grant_type' => 'client_credentials',
                'client_id' => $this->oauthClient->id,
                'client_secret' => 'secure-secret',
                'scope' => '',
            ]);
        }
        
        // At least some requests should succeed (rate limiting may not be strict in tests)
        $successCount = collect($responses)->filter(fn($response) => $response->status() < 400)->count();
        $this->assertGreaterThan(0, $successCount);
    }

    public function test_user_logout_revokes_all_tokens()
    {
        $this->assertUsingTestDatabase();
        
        // Create multiple tokens for the user
        $tokens = Token::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'client_id' => $this->oauthClient->id,
            'expires_at' => now()->addDay(),
            'revoked' => false,
        ]);
        
        // Login user and then logout
        $this->actingAs($this->user);
        $this->post('/logout');
        
        // Verify tokens are still valid (logout doesn't automatically revoke API tokens)
        foreach ($tokens as $token) {
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token->id,
            ])->get('/api/user');
            
            // API tokens should still work after web logout
            $this->assertEquals(200, $response->status());
        }
    }

    protected function tearDown(): void
    {
        // Verify database isolation is maintained
        $this->assertUsingTestDatabase();
        
        parent::tearDown();
    }
}
