<?php

namespace Tests\Feature\Auth;

use App\Models\Passport\Client;
use App\Models\User;
use App\Notifications\Auth\VerifyEmail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class RegistrationFlowTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Assert we're using the test database for every test
        $this->assertUsingTestDatabase();
    }

    public function test_database_isolation_is_working()
    {
        // Verify we're using SQLite in-memory database
        $this->assertUsingTestDatabase();
        
        // Verify development database configuration is preserved
        $this->assertDevelopmentDatabasePreserved();
        
        // Log database info for debugging
        $dbInfo = $this->getDatabaseInfo();
        $this->assertEquals('testing', $dbInfo['environment']);
        $this->assertEquals('sqlite', $dbInfo['default_connection']);
        $this->assertEquals(':memory:', $dbInfo['sqlite_database']);
    }

    public function test_user_can_view_registration_form()
    {
        $this->assertUsingTestDatabase();
        
        $response = $this->get('/register');
        
        $response->assertStatus(200);
        $response->assertViewIs('auth.register');
        $response->assertSee('Register');
    }

    public function test_user_can_register_with_valid_data()
    {
        $this->assertUsingTestDatabase();
        Notification::fake();

        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>', // Use gmail.com to bypass ValidEmailProvider rule
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        // Ensure no users exist before registration
        $this->assertEquals(0, User::count());

        $response = $this->post('/register', $userData);

        // Debug the response if it fails
        if ($response->getStatusCode() !== 302) {
            dump('Response status: ' . $response->getStatusCode());
            dump('Response content: ' . $response->getContent());
            dump('Session errors: ' . json_encode(session('errors')?->all()));
        }

        // Should redirect (Laravel's RegistersUsers trait redirects to home by default, then to email verification)
        $response->assertRedirect();

        // User should be created in test database
        $this->assertEquals(1, User::count());
        $this->assertDatabaseHas('users', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'email_verified_at' => null, // Should be unverified initially
        ]);

        // Password should be hashed
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertTrue(Hash::check('password123', $user->password));

        // Email verification notification should be sent
        Notification::assertSentTo($user, VerifyEmail::class);

        // User should be logged out after registration
        $this->assertGuest();

        // Session should contain verify_user_id
        $this->assertTrue(Session::has('verify_user_id'));
        $this->assertEquals($user->id, Session::get('verify_user_id'));

        // Subscription should be created
        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $user->id,
            'plan_id' => 1,
            'status' => 'active',
        ]);
    }

    public function test_registration_fails_with_invalid_data()
    {
        $this->assertUsingTestDatabase();

        $invalidData = [
            'name' => '', // Required
            'email' => 'invalid-email', // Invalid format
            'password' => '123', // Too short
            'password_confirmation' => 'different', // Doesn't match
        ];

        $response = $this->post('/register', $invalidData);

        // Should redirect back with errors or show an error
        $response->assertRedirect();

        // No user should be created
        $this->assertEquals(0, User::count());

        // Should have some kind of error indication
        $this->assertTrue(
            session()->has('errors') || session()->has('error'),
            'Expected validation errors or error message in session'
        );
    }

    public function test_registration_fails_with_duplicate_email()
    {
        $this->assertUsingTestDatabase();

        // Create existing user
        User::factory()->create(['email' => '<EMAIL>']);

        $userData = [
            'name' => 'New User',
            'email' => '<EMAIL>', // Duplicate email
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->post('/register', $userData);

        $response->assertSessionHasErrors();

        // Should still only have one user
        $this->assertEquals(1, User::count());
    }

    public function test_registration_with_oauth_parameters()
    {
        $this->assertUsingTestDatabase();
        Notification::fake();

        // Set up OAuth session data
        Session::put('redirect_uri', 'http://example.com/oauth/authorize?client_id=123&redirect_uri=http://callback.com&response_type=code&is_register=1');
        Session::put('oauth_flow_type', 'registration');
        Session::put('is_first_time', true);

        $userData = [
            'name' => 'OAuth User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->post('/register', $userData);

        $response->assertRedirect();

        // User should be created
        $this->assertDatabaseHas('users', [
            'name' => 'OAuth User',
            'email' => '<EMAIL>',
        ]);

        // OAuth session data should be preserved for valid registration flow
        $this->assertTrue(Session::has('redirect_uri'));
    }

    public function test_registration_clears_invalid_oauth_session_data()
    {
        $this->assertUsingTestDatabase();
        
        // Set up invalid OAuth session data (login flow type)
        Session::put('redirect_uri', 'http://example.com/oauth/authorize?client_id=123');
        Session::put('oauth_flow_type', 'login'); // Wrong flow type for registration
        
        $response = $this->get('/register');
        
        $response->assertStatus(200);
        
        // Invalid session data should be cleared
        $this->assertFalse(Session::has('oauth_flow_type'));
    }

    public function test_registration_with_email_parameter()
    {
        $this->assertUsingTestDatabase();

        // Set up session data to simulate OAuth flow with email parameter
        Session::put('redirect_uri', 'http://example.com/oauth/authorize?client_id=123&is_register=1&email=<EMAIL>');
        Session::put('oauth_flow_type', 'registration');
        Session::put('is_first_time', true);

        $response = $this->get('/register');

        $response->assertStatus(200);
        $response->assertViewIs('auth.register');
        // The email should be prefilled from the OAuth flow
        $response->assertViewHas('prefill_email');
    }

    public function test_guest_middleware_redirects_authenticated_users()
    {
        $this->assertUsingTestDatabase();

        /** @var User $user */
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/register');

        $response->assertRedirect('/home');
    }

    public function test_database_isolation_between_tests()
    {
        $this->assertUsingTestDatabase();
        
        // This test should start with a clean database
        $this->assertEquals(0, User::count());
        
        // Create a user
        User::factory()->create(['email' => '<EMAIL>']);
        
        $this->assertEquals(1, User::count());
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
    }

    public function test_database_isolation_verification()
    {
        $this->assertUsingTestDatabase();

        // This test should also start with a clean database
        // The user from the previous test should not exist
        $this->assertEquals(0, User::count());
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);

        // Create a different user to verify isolation
        User::factory()->create(['email' => '<EMAIL>']);

        $this->assertEquals(1, User::count());
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
    }

    public function test_sso_registration_with_is_register_parameter_logs_out_current_user()
    {
        $this->assertUsingTestDatabase();

        // Create and login an existing user
        /** @var User $existingUser */
        $existingUser = User::factory()->create([
            'email' => '<EMAIL>',
            'verified' => 1,
        ]);

        $this->actingAs($existingUser);
        $this->assertTrue(Auth::check());

        // Create OAuth client
        $client = Client::factory()->create([
            'id' => 'sso-register-client',
            'redirect' => 'http://sso.test.com/oauth/callback',
        ]);

        // Set up OAuth parameters with is_register=1
        $oauthParams = [
            'client_id' => $client->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ];

        // Access OAuth authorization with registration parameters
        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should redirect to login (user should be logged out)
        $response->assertRedirect('/login');

        // Verify user was logged out due to is_register=1
        $this->assertFalse(Auth::check());

        // Verify OAuth session data is preserved
        $this->assertTrue(Session::has('redirect_uri'));
        $this->assertEquals('registration', Session::get('oauth_flow_type'));
    }

    public function test_sso_registration_email_existence_validation()
    {
        $this->assertUsingTestDatabase();

        // Create existing user
        $existingUser = User::factory()->create([
            'email' => '<EMAIL>',
            'verified' => 1,
        ]);

        // Create OAuth client
        $client = Client::factory()->create([
            'id' => 'email-check-client',
            'redirect' => 'http://sso.test.com/oauth/callback',
        ]);

        // Set up OAuth parameters with existing email
        $oauthParams = [
            'client_id' => $client->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $existingUser->email, // Existing email
        ];

        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should redirect to login page
        $response->assertRedirect('/login');

        // Follow redirect to login
        $response = $this->get('/login');

        // Should redirect to registration with error about existing email
        $response->assertRedirect();

        // Verify session contains registration flow data
        $this->assertEquals('registration', Session::get('oauth_flow_type'));
    }

    public function test_sso_registration_redirect_to_appropriate_pages()
    {
        $this->assertUsingTestDatabase();

        // Create OAuth client
        $client = Client::factory()->create([
            'id' => 'redirect-test-client',
            'redirect' => 'http://sso.test.com/oauth/callback',
        ]);

        // Set up OAuth parameters for registration
        $oauthParams = [
            'client_id' => $client->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ];

        // Access OAuth authorization
        $response = $this->get('/oauth/authorize?' . http_build_query($oauthParams));

        // Should redirect to login first
        $response->assertRedirect('/login');

        // Follow redirect to login
        $response = $this->get('/login');

        // Should redirect to registration page
        $response->assertRedirect();

        // Follow redirect to registration
        $response = $this->get('/register');

        // Should show registration form with prefilled email
        $response->assertStatus(200);
        $response->assertViewIs('auth.register');
        $response->assertViewHas('prefill_email', '<EMAIL>');
    }

    public function test_sso_registration_completes_oauth_flow_after_registration()
    {
        $this->assertUsingTestDatabase();
        Notification::fake();

        // Create OAuth client
        $client = Client::factory()->create([
            'id' => 'complete-flow-client',
            'redirect' => 'http://sso.test.com/oauth/callback',
        ]);

        // Set up OAuth session data for registration
        Session::put('redirect_uri', '/oauth/authorize?' . http_build_query([
            'client_id' => $client->id,
            'redirect_uri' => 'http://sso.test.com/oauth/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
        ]));
        Session::put('oauth_flow_type', 'registration');
        Session::put('is_first_time', true);

        $userData = [
            'name' => 'SSO New User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->post('/register', $userData);

        // Should redirect to email verification
        $response->assertRedirect('/email/verify');

        // User should be created
        $this->assertDatabaseHas('users', [
            'name' => 'SSO New User',
            'email' => '<EMAIL>',
        ]);

        // OAuth session data should be preserved for post-verification flow
        $this->assertTrue(Session::has('redirect_uri'));
        $this->assertEquals('registration', Session::get('oauth_flow_type'));
    }
}
